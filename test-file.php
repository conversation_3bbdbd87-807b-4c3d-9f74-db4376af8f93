<?php
/**
 * Test Laravel Script
 * This is a simple test file for SafeScripter encryption
 */

// Load Laravel dependencies
require $baseDir . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable($baseDir);
$dotenv->load();

class TestScript {
    public function __construct() {
        echo "SafeScripter Test Script Initialized\n";
    }
    
    public function runTest() {
        $message = "Hello from SafeScripter!";
        echo $message . "\n";
        
        // Test database connection
        try {
            $users = DB::table('users')->count();
            echo "Database connected. Users count: " . $users . "\n";
        } catch (Exception $e) {
            echo "Database connection failed: " . $e->getMessage() . "\n";
        }
        
        return true;
    }
    
    private function processData($data) {
        // Some processing logic
        $processed = array_map(function($item) {
            return strtoupper($item);
        }, $data);
        
        return $processed;
    }
}

// Initialize and run
$script = new TestScript();
$result = $script->runTest();

if ($result) {
    echo "Test completed successfully!\n";
} else {
    echo "Test failed!\n";
}

?>
