import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface EncryptionConfig {
  level: 'basic' | 'advanced' | 'maximum'
  productionSafe: boolean
  loadBalancerCompatible: boolean
  antiDebugging: boolean
  integrityCheck: boolean
  domainLocking: boolean
  allowedDomains: string[]
  
  // Basic obfuscation
  minify: boolean
  removeComments: boolean
  removeWhitespace: boolean
  renameVariables: boolean
  
  // Advanced obfuscation
  stringEncoding: boolean
  controlFlow: boolean
  deadCode: boolean
  functionSplit: boolean
  
  // Security features
  antiDebug: boolean
  expiration: boolean
  
  // Encryption options
  base64: boolean
  aes: boolean
  hexEncode: boolean
  customKey: boolean
  
  // Advanced settings
  obfuscationStrength: number
  deadCodeInjection: number
  stringEncodingLayers: number
}

interface ConfigurationPanelProps {
  config: EncryptionConfig
  onConfigChange: (config: EncryptionConfig) => void
}

export default function ConfigurationPanel({ config, onConfigChange }: ConfigurationPanelProps) {
  const [newDomain, setNewDomain] = useState('')

  const updateConfig = (updates: Partial<EncryptionConfig>) => {
    onConfigChange({ ...config, ...updates })
  }

  const addDomain = () => {
    if (newDomain.trim() && !config.allowedDomains.includes(newDomain.trim())) {
      updateConfig({
        allowedDomains: [...config.allowedDomains, newDomain.trim()]
      })
      setNewDomain('')
    }
  }

  const removeDomain = (domain: string) => {
    updateConfig({
      allowedDomains: config.allowedDomains.filter(d => d !== domain)
    })
  }

  const calculateProtectionLevel = () => {
    let score = 0
    const weights = {
      level: config.level === 'maximum' ? 30 : config.level === 'advanced' ? 20 : 10,
      antiDebugging: config.antiDebugging ? 15 : 0,
      stringEncoding: config.stringEncoding ? 10 : 0,
      controlFlow: config.controlFlow ? 10 : 0,
      deadCode: config.deadCode ? 8 : 0,
      integrityCheck: config.integrityCheck ? 8 : 0,
      domainLocking: config.domainLocking ? 7 : 0,
      base64: config.base64 ? 5 : 0,
      aes: config.aes ? 12 : 0,
      strength: Math.floor(config.obfuscationStrength / 10)
    }
    
    score = Object.values(weights).reduce((sum, val) => sum + val, 0)
    return Math.min(100, score)
  }

  const protectionLevel = calculateProtectionLevel()

  return (
    <div className="space-y-6">
      {/* Encryption Level */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔐 Encryption Level
          </CardTitle>
          <CardDescription>
            Choose the base protection level for your files
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { value: 'basic', label: 'Basic Protection', desc: 'Fast, reliable, production-safe' },
              { value: 'advanced', label: 'Advanced Encryption', desc: 'Multi-layer, high security' },
              { value: 'maximum', label: 'Maximum Security', desc: 'Military-grade protection' }
            ].map((option) => (
              <div
                key={option.value}
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  config.level === option.value
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-blue-300'
                }`}
                onClick={() => updateConfig({ level: option.value as any })}
              >
                <div className="flex items-center gap-2 mb-2">
                  <div className={`w-4 h-4 rounded-full border-2 ${
                    config.level === option.value
                      ? 'border-blue-500 bg-blue-500'
                      : 'border-gray-300'
                  }`}>
                    {config.level === option.value && (
                      <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
                    )}
                  </div>
                  <span className="font-semibold">{option.label}</span>
                </div>
                <p className="text-sm text-gray-600">{option.desc}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Basic Obfuscation Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔧 Basic Obfuscation
          </CardTitle>
          <CardDescription>
            Essential protection features for code security
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              { key: 'minify', label: 'Code Minification', desc: 'Remove unnecessary whitespace and formatting' },
              { key: 'removeComments', label: 'Remove Comments', desc: 'Strip all comments from source code' },
              { key: 'removeWhitespace', label: 'Remove Whitespace', desc: 'Eliminate extra whitespace characters' },
              { key: 'renameVariables', label: 'Rename Variables', desc: 'Obfuscate variable and function names' }
            ].map((option) => (
              <div key={option.key} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <Checkbox
                  id={option.key}
                  checked={config[option.key as keyof EncryptionConfig] as boolean}
                  onCheckedChange={(checked) => updateConfig({ [option.key]: checked })}
                  className="mt-1"
                />
                <div className="flex-1">
                  <label htmlFor={option.key} className="font-medium text-gray-900 cursor-pointer">
                    {option.label}
                  </label>
                  <p className="text-sm text-gray-600 mt-1">{option.desc}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Obfuscation Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🚀 Advanced Obfuscation
          </CardTitle>
          <CardDescription>
            Professional-grade protection mechanisms
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              { key: 'stringEncoding', label: 'String Encoding', desc: 'Encode string literals with multiple layers' },
              { key: 'controlFlow', label: 'Control Flow Obfuscation', desc: 'Restructure code execution flow' },
              { key: 'deadCode', label: 'Dead Code Injection', desc: 'Add non-functional code for confusion' },
              { key: 'functionSplit', label: 'Function Splitting', desc: 'Break functions into smaller parts' }
            ].map((option) => (
              <div key={option.key} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <Checkbox
                  id={option.key}
                  checked={config[option.key as keyof EncryptionConfig] as boolean}
                  onCheckedChange={(checked) => updateConfig({ [option.key]: checked })}
                  className="mt-1"
                />
                <div className="flex-1">
                  <label htmlFor={option.key} className="font-medium text-gray-900 cursor-pointer">
                    {option.label}
                  </label>
                  <p className="text-sm text-gray-600 mt-1">{option.desc}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Security Features */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🛡️ Security Features
          </CardTitle>
          <CardDescription>
            Anti-debugging and integrity protection
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { key: 'antiDebugging', label: 'Anti-Debugging', desc: 'Prevent debugging and inspection' },
                { key: 'integrityCheck', label: 'Integrity Checking', desc: 'Detect file tampering attempts' },
                { key: 'productionSafe', label: 'Production Safe', desc: 'Prevent HTTP 500 errors on servers' },
                { key: 'loadBalancerCompatible', label: 'Load Balancer Compatible', desc: 'Works with proxies and CDNs' }
              ].map((option) => (
                <div key={option.key} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Checkbox
                    id={option.key}
                    checked={config[option.key as keyof EncryptionConfig] as boolean}
                    onCheckedChange={(checked) => updateConfig({ [option.key]: checked })}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <label htmlFor={option.key} className="font-medium text-gray-900 cursor-pointer">
                      {option.label}
                    </label>
                    <p className="text-sm text-gray-600 mt-1">{option.desc}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Domain Locking */}
            <div className="border-t pt-4">
              <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg mb-4">
                <Checkbox
                  id="domainLocking"
                  checked={config.domainLocking}
                  onCheckedChange={(checked) => updateConfig({ domainLocking: checked })}
                  className="mt-1"
                />
                <div className="flex-1">
                  <label htmlFor="domainLocking" className="font-medium text-gray-900 cursor-pointer">
                    Domain Locking
                  </label>
                  <p className="text-sm text-gray-600 mt-1">Restrict code execution to specific domains</p>
                </div>
              </div>

              {config.domainLocking && (
                <div className="ml-7 space-y-3">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter domain (e.g., yoursite.com)"
                      value={newDomain}
                      onChange={(e) => setNewDomain(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addDomain()}
                      className="flex-1"
                    />
                    <Button onClick={addDomain} size="sm">
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {config.allowedDomains.map((domain) => (
                      <Badge key={domain} variant="secondary" className="flex items-center gap-1">
                        {domain}
                        <button
                          onClick={() => removeDomain(domain)}
                          className="ml-1 text-red-500 hover:text-red-700"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Encryption Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔐 Encryption Options
          </CardTitle>
          <CardDescription>
            Multi-layer encryption for maximum security
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              { key: 'base64', label: 'Base64 Encoding', desc: 'Standard base64 content encoding' },
              { key: 'aes', label: 'AES Encryption', desc: 'Advanced Encryption Standard (256-bit)' },
              { key: 'hexEncode', label: 'Hex Encoding', desc: 'Hexadecimal encoding transformation' },
              { key: 'customKey', label: 'Custom Key Generation', desc: 'Generate unique encryption keys' }
            ].map((option) => (
              <div key={option.key} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <Checkbox
                  id={option.key}
                  checked={config[option.key as keyof EncryptionConfig] as boolean}
                  onCheckedChange={(checked) => updateConfig({ [option.key]: checked })}
                  className="mt-1"
                />
                <div className="flex-1">
                  <label htmlFor={option.key} className="font-medium text-gray-900 cursor-pointer">
                    {option.label}
                  </label>
                  <p className="text-sm text-gray-600 mt-1">{option.desc}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🎯 Advanced Settings
          </CardTitle>
          <CardDescription>
            Fine-tune protection parameters
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="font-medium text-gray-900">Obfuscation Strength</label>
                <Badge variant="outline">{config.obfuscationStrength}%</Badge>
              </div>
              <input
                type="range"
                min="10"
                max="100"
                value={config.obfuscationStrength}
                onChange={(e) => updateConfig({ obfuscationStrength: parseInt(e.target.value) })}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="font-medium text-gray-900">Dead Code Injection</label>
                <Badge variant="outline">{config.deadCodeInjection}%</Badge>
              </div>
              <input
                type="range"
                min="0"
                max="100"
                value={config.deadCodeInjection}
                onChange={(e) => updateConfig({ deadCodeInjection: parseInt(e.target.value) })}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="font-medium text-gray-900">String Encoding Layers</label>
                <Badge variant="outline">{config.stringEncodingLayers}</Badge>
              </div>
              <input
                type="range"
                min="1"
                max="10"
                value={config.stringEncodingLayers}
                onChange={(e) => updateConfig({ stringEncodingLayers: parseInt(e.target.value) })}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Protection Level Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📊 Protection Level
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-lg font-semibold">Overall Protection</span>
              <Badge 
                variant={protectionLevel >= 80 ? "default" : protectionLevel >= 60 ? "secondary" : "outline"}
                className="text-lg px-3 py-1"
              >
                {protectionLevel}%
              </Badge>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className={`h-3 rounded-full transition-all duration-300 ${
                  protectionLevel >= 80 ? 'bg-green-500' :
                  protectionLevel >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                }`}
                style={{ width: `${protectionLevel}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600">
              {protectionLevel >= 80 ? 'Maximum security - Excellent protection against reverse engineering' :
               protectionLevel >= 60 ? 'Good security - Solid protection for most use cases' :
               'Basic security - Consider enabling more protection features'}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
