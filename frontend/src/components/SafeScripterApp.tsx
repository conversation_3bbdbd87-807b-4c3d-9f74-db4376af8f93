import { useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'

// Types for our application
interface FileData {
  id: string
  filename: string
  content: string
  size: number
}

interface ProcessedFile {
  id: string
  filename: string
  original_name: string
  encrypted_name: string
  size: number
  original_size: number
  encrypted_size: number
  size_increase_percent: number
  compression_ratio: number
  status: 'pending' | 'processing' | 'completed' | 'error'
  error_message?: string
  processing_time?: number
  download_url: string
  downloadUrl: string
  protection_level: number
  encryption_config: any
  created_at?: string
  processed_at?: string
}

interface EncryptionConfig {
  level: 'basic' | 'advanced' | 'maximum'
  productionSafe: boolean
  loadBalancerCompatible: boolean
  antiDebugging: boolean
  integrityCheck: boolean
  domainLocking: boolean
  allowedDomains: string[]
  
  // Basic obfuscation
  minify: boolean
  removeComments: boolean
  removeWhitespace: boolean
  renameVariables: boolean
  
  // Advanced obfuscation
  stringEncoding: boolean
  controlFlow: boolean
  deadCode: boolean
  functionSplit: boolean
  
  // Security features
  antiDebug: boolean
  expiration: boolean
  
  // Encryption options
  base64: boolean
  aes: boolean
  hexEncode: boolean
  customKey: boolean
  
  // Advanced settings
  obfuscationStrength: number
  deadCodeInjection: number
  stringEncodingLayers: number
}

const defaultConfig: EncryptionConfig = {
  level: 'basic',
  productionSafe: true,
  loadBalancerCompatible: true,
  antiDebugging: true,
  integrityCheck: false,
  domainLocking: false,
  allowedDomains: ['localhost'],
  
  minify: true,
  removeComments: true,
  removeWhitespace: true,
  renameVariables: true,
  
  stringEncoding: true,
  controlFlow: true,
  deadCode: true,
  functionSplit: true,
  
  antiDebug: true,
  expiration: true,
  
  base64: true,
  aes: false,
  hexEncode: true,
  customKey: true,
  
  obfuscationStrength: 70,
  deadCodeInjection: 50,
  stringEncodingLayers: 3,
}

export default function SafeScripterApp() {
  const [selectedFiles, setSelectedFiles] = useState<FileData[]>([])
  const [processedFiles, setProcessedFiles] = useState<ProcessedFile[]>([])
  const [config, setConfig] = useState<EncryptionConfig>(defaultConfig)
  const [isProcessing, setIsProcessing] = useState(false)
  const [dragOver, setDragOver] = useState(false)

  // File upload handlers
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      handleFiles(Array.from(files))
    }
  }, [])

  const handleFiles = useCallback(async (files: File[]) => {
    const newFiles: FileData[] = []
    
    for (const file of files) {
      try {
        const content = await readFileContent(file)
        newFiles.push({
          id: crypto.randomUUID(),
          filename: file.name,
          content,
          size: file.size
        })
      } catch (error) {
        console.error(`Error reading file ${file.name}:`, error)
      }
    }
    
    setSelectedFiles(prev => [...prev, ...newFiles])
  }, [])

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = reject
      reader.readAsText(file)
    })
  }

  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    handleFiles(files)
  }, [handleFiles])

  // Encryption process
  const startEncryption = async () => {
    if (selectedFiles.length === 0) return

    setIsProcessing(true)
    
    try {
      const response = await fetch('http://localhost:8000/api/encrypt/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          files: selectedFiles.map(file => ({
            filename: file.filename,
            content: file.content
          })),
          encryptionConfig: config
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      
      if (data.status === 'success') {
        setProcessedFiles(data.results)
        // Clear selected files after successful processing
        setSelectedFiles([])
      } else {
        throw new Error(data.error || 'Encryption failed')
      }
    } catch (error) {
      console.error('Encryption error:', error)
      alert(`Encryption failed: ${error}`)
    } finally {
      setIsProcessing(false)
    }
  }

  // Download handlers
  const downloadFile = (fileId: string) => {
    window.open(`http://localhost:8000/api/download/${fileId}/`, '_blank')
  }

  const downloadAll = () => {
    window.open('http://localhost:8000/api/download-all/', '_blank')
  }

  // Clear files
  const clearFiles = async () => {
    try {
      await fetch('http://localhost:8000/api/clear/', {
        method: 'POST'
      })
      setProcessedFiles([])
      setSelectedFiles([])
    } catch (error) {
      console.error('Clear files error:', error)
    }
  }

  const removeSelectedFile = (fileId: string) => {
    setSelectedFiles(prev => prev.filter(f => f.id !== fileId))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="min-h-screen" style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
    }}>
      <div className="max-w-6xl mx-auto p-5 space-y-8">
        {/* Header */}
        <Card className="relative overflow-hidden">
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-red-500 to-purple-600"></div>
          <CardHeader className="text-center py-6 px-10">
            <CardTitle className="text-4xl font-bold text-gray-800 mb-2">
              🔐 Advanced Code Obfuscator
            </CardTitle>
            <CardDescription className="text-lg text-gray-600 max-w-2xl mx-auto">
              Military-grade code protection with multi-layer obfuscation, encryption, and anti-debugging measures
            </CardDescription>
          </CardHeader>
        </Card>

        {/* File Upload Section */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-4 text-xl font-semibold text-gray-800">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center text-white text-2xl">
                📁
              </div>
              File Upload & Processing
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Upload Zone */}
            <div
              className={`border-3 border-dashed rounded-2xl p-12 text-center transition-all duration-300 cursor-pointer relative overflow-hidden ${
                dragOver
                  ? 'border-red-500 bg-red-50 scale-105'
                  : 'border-gray-300 bg-gray-50 hover:border-red-400 hover:bg-red-50'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => document.getElementById('fileInput')?.click()}
            >
              <div className="text-6xl text-gray-400 mb-5">📄</div>
              <div className="text-xl text-gray-700 mb-2">Drop files here or click to browse</div>
              <div className="text-gray-500">Supports: HTML, CSS, JavaScript, PHP files</div>
              <input
                type="file"
                id="fileInput"
                multiple
                accept=".html,.css,.js,.php,.htm,.jsx,.ts,.tsx"
                onChange={handleFileSelect}
                className="absolute top-0 left-0 w-full h-full opacity-0 cursor-pointer z-10"
              />
            </div>

            {/* Selected Files List */}
            {selectedFiles.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Selected Files ({selectedFiles.length})</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {selectedFiles.map((file) => (
                    <div key={file.id} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-500 text-white rounded flex items-center justify-center text-sm font-bold">
                          {file.filename.split('.').pop()?.toUpperCase().slice(0, 2) || 'F'}
                        </div>
                        <div>
                          <div className="font-medium text-gray-800">{file.filename}</div>
                          <div className="text-sm text-gray-500">{formatFileSize(file.size)}</div>
                        </div>
                      </div>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => removeSelectedFile(file.id)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-5 mt-8">
              <div className="bg-white rounded-xl p-5 text-center shadow-md hover:shadow-lg transition-shadow">
                <div className="text-2xl font-bold text-red-600">{selectedFiles.length}</div>
                <div className="text-sm text-gray-500 uppercase tracking-wide">Files Queued</div>
              </div>
              <div className="bg-white rounded-xl p-5 text-center shadow-md hover:shadow-lg transition-shadow">
                <div className="text-2xl font-bold text-red-600">
                  {formatFileSize(selectedFiles.reduce((sum, f) => sum + f.size, 0))}
                </div>
                <div className="text-sm text-gray-500 uppercase tracking-wide">Total Size</div>
              </div>
              <div className="bg-white rounded-xl p-5 text-center shadow-md hover:shadow-lg transition-shadow">
                <div className="text-2xl font-bold text-red-600">{processedFiles.length}</div>
                <div className="text-sm text-gray-500 uppercase tracking-wide">Completed</div>
              </div>
              <div className="bg-white rounded-xl p-5 text-center shadow-md hover:shadow-lg transition-shadow">
                <div className="text-2xl font-bold text-red-600">100%</div>
                <div className="text-sm text-gray-500 uppercase tracking-wide">Protection Level</div>
              </div>
            </div>

          </CardContent>
        </Card>

        {/* Obfuscation Configuration */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-4 text-xl font-semibold text-gray-800">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center text-white text-2xl">
                ⚙️
              </div>
              Obfuscation Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Options Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {/* Basic Obfuscation */}
              <div className="bg-gray-50 rounded-xl p-6 border-2 border-gray-200 hover:border-red-400 transition-colors">
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-2xl">🔧</span>
                  <h3 className="text-lg font-semibold text-gray-800">Basic Obfuscation</h3>
                </div>
                <p className="text-gray-600 text-sm mb-5">Essential protection features for code security</p>
                <div className="space-y-3">
                  {[
                    { id: 'minify', label: 'Code Minification', checked: config.minify },
                    { id: 'removeComments', label: 'Remove Comments', checked: config.removeComments },
                    { id: 'removeWhitespace', label: 'Remove Whitespace', checked: config.removeWhitespace },
                    { id: 'renameVariables', label: 'Rename Variables', checked: config.renameVariables }
                  ].map((option) => (
                    <div key={option.id} className="flex items-center gap-3 p-2 bg-white rounded-lg hover:bg-gray-50 transition-colors">
                      <Checkbox
                        id={option.id}
                        checked={option.checked}
                        onCheckedChange={(checked) => setConfig(prev => ({ ...prev, [option.id]: checked }))}
                        className="w-5 h-5"
                      />
                      <label htmlFor={option.id} className="text-sm font-medium text-gray-700 cursor-pointer flex-1">
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Advanced Obfuscation */}
              <div className="bg-gray-50 rounded-xl p-6 border-2 border-gray-200 hover:border-red-400 transition-colors">
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-2xl">🚀</span>
                  <h3 className="text-lg font-semibold text-gray-800">Advanced Obfuscation</h3>
                </div>
                <p className="text-gray-600 text-sm mb-5">Professional-grade protection mechanisms</p>
                <div className="space-y-3">
                  {[
                    { id: 'stringEncoding', label: 'String Encoding', checked: config.stringEncoding },
                    { id: 'controlFlow', label: 'Control Flow Obfuscation', checked: config.controlFlow },
                    { id: 'deadCode', label: 'Dead Code Injection', checked: config.deadCode },
                    { id: 'functionSplit', label: 'Function Splitting', checked: config.functionSplit }
                  ].map((option) => (
                    <div key={option.id} className="flex items-center gap-3 p-2 bg-white rounded-lg hover:bg-gray-50 transition-colors">
                      <Checkbox
                        id={option.id}
                        checked={option.checked}
                        onCheckedChange={(checked) => setConfig(prev => ({ ...prev, [option.id]: checked }))}
                        className="w-5 h-5"
                      />
                      <label htmlFor={option.id} className="text-sm font-medium text-gray-700 cursor-pointer flex-1">
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Security Features */}
              <div className="bg-gray-50 rounded-xl p-6 border-2 border-gray-200 hover:border-red-400 transition-colors">
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-2xl">🛡️</span>
                  <h3 className="text-lg font-semibold text-gray-800">Security Features</h3>
                </div>
                <p className="text-gray-600 text-sm mb-5">Anti-debugging and integrity protection</p>
                <div className="space-y-3">
                  {[
                    { id: 'antiDebug', label: 'Anti-Debugging', checked: config.antiDebug },
                    { id: 'integrityCheck', label: 'Integrity Checking', checked: config.integrityCheck },
                    { id: 'domainLocking', label: 'Domain Locking', checked: config.domainLocking },
                    { id: 'expiration', label: 'Code Expiration', checked: config.expiration }
                  ].map((option) => (
                    <div key={option.id} className="flex items-center gap-3 p-2 bg-white rounded-lg hover:bg-gray-50 transition-colors">
                      <Checkbox
                        id={option.id}
                        checked={option.checked}
                        onCheckedChange={(checked) => setConfig(prev => ({ ...prev, [option.id]: checked }))}
                        className="w-5 h-5"
                      />
                      <label htmlFor={option.id} className="text-sm font-medium text-gray-700 cursor-pointer flex-1">
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Encryption Options */}
              <div className="bg-gray-50 rounded-xl p-6 border-2 border-gray-200 hover:border-red-400 transition-colors">
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-2xl">🔐</span>
                  <h3 className="text-lg font-semibold text-gray-800">Encryption Options</h3>
                </div>
                <p className="text-gray-600 text-sm mb-5">Multi-layer encryption for maximum security</p>
                <div className="space-y-3">
                  {[
                    { id: 'base64', label: 'Base64 Encoding', checked: config.base64 },
                    { id: 'aes', label: 'AES Encryption', checked: config.aes },
                    { id: 'hexEncode', label: 'Hex Encoding', checked: config.hexEncode },
                    { id: 'customKey', label: 'Custom Key Generation', checked: config.customKey }
                  ].map((option) => (
                    <div key={option.id} className="flex items-center gap-3 p-2 bg-white rounded-lg hover:bg-gray-50 transition-colors">
                      <Checkbox
                        id={option.id}
                        checked={option.checked}
                        onCheckedChange={(checked) => setConfig(prev => ({ ...prev, [option.id]: checked }))}
                        className="w-5 h-5"
                      />
                      <label htmlFor={option.id} className="text-sm font-medium text-gray-700 cursor-pointer flex-1">
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Advanced Configuration */}
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border-2 border-gray-200 mb-8">
              <div className="flex items-center gap-3 mb-6">
                <span className="text-2xl">🎯</span>
                <h3 className="text-xl font-semibold text-gray-800">Advanced Configuration</h3>
              </div>

              <div className="space-y-6">
                <div>
                  <div className="flex justify-between items-center mb-3">
                    <label className="font-medium text-gray-800">Obfuscation Strength</label>
                    <Badge variant="outline" className="bg-red-500 text-white border-red-500">
                      {config.obfuscationStrength}%
                    </Badge>
                  </div>
                  <input
                    type="range"
                    min="10"
                    max="100"
                    value={config.obfuscationStrength}
                    onChange={(e) => setConfig(prev => ({ ...prev, obfuscationStrength: parseInt(e.target.value) }))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    style={{
                      background: `linear-gradient(to right, #ef4444 0%, #f97316 50%, #22c55e 100%)`
                    }}
                  />
                </div>

                <div>
                  <div className="flex justify-between items-center mb-3">
                    <label className="font-medium text-gray-800">Dead Code Injection</label>
                    <Badge variant="outline" className="bg-red-500 text-white border-red-500">
                      {config.deadCodeInjection}%
                    </Badge>
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={config.deadCodeInjection}
                    onChange={(e) => setConfig(prev => ({ ...prev, deadCodeInjection: parseInt(e.target.value) }))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    style={{
                      background: `linear-gradient(to right, #ef4444 0%, #f97316 50%, #22c55e 100%)`
                    }}
                  />
                </div>

                <div>
                  <div className="flex justify-between items-center mb-3">
                    <label className="font-medium text-gray-800">String Encoding Layers</label>
                    <Badge variant="outline" className="bg-red-500 text-white border-red-500">
                      {config.stringEncodingLayers}
                    </Badge>
                  </div>
                  <input
                    type="range"
                    min="1"
                    max="10"
                    value={config.stringEncodingLayers}
                    onChange={(e) => setConfig(prev => ({ ...prev, stringEncodingLayers: parseInt(e.target.value) }))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    style={{
                      background: `linear-gradient(to right, #ef4444 0%, #f97316 50%, #22c55e 100%)`
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Protection Strength Meter */}
            <div className="mb-8">
              <div className="bg-gray-200 h-2 rounded-full overflow-hidden mb-3">
                <div
                  className="h-full bg-gradient-to-r from-red-500 via-orange-500 to-green-500 transition-all duration-300"
                  style={{ width: '85%' }}
                ></div>
              </div>
              <div className="text-center font-semibold text-gray-800">Maximum Protection</div>
            </div>

            {/* Active Security Features */}
            <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-6 border-l-4 border-red-500 mb-8">
              <div className="flex items-center gap-3 mb-5">
                <span className="text-2xl">🔒</span>
                <h3 className="text-xl font-semibold text-gray-800">Active Security Features</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  'Multi-layer string encoding with dynamic keys',
                  'Variable name scrambling with random patterns',
                  'Dead code injection for reverse engineering protection',
                  'Anti-debugging measures with detection bypass',
                  'Control flow obfuscation with fake branches',
                  'Integrity checking with hash validation',
                  'Domain locking for authorized deployment only',
                  'Code expiration with time-based validation'
                ].map((feature, index) => (
                  <div key={index} className="flex items-center gap-3 text-gray-700">
                    <span className="text-lg">🔒</span>
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Encryption Configuration Panel */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border-l-4 border-blue-500 mb-8">
              <div className="flex items-center gap-3 mb-6">
                <span className="text-2xl">⚙️</span>
                <h3 className="text-xl font-semibold text-gray-800">Encryption Configuration</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Encryption Level */}
                <div>
                  <h4 className="flex items-center gap-2 text-lg font-semibold text-gray-800 mb-4">
                    🔐 Encryption Level
                  </h4>
                  <div className="space-y-3">
                    {[
                      { value: 'basic', label: 'Basic Protection', desc: 'Fast, reliable, production-safe' },
                      { value: 'advanced', label: 'Advanced Encryption', desc: 'Multi-layer, high security' },
                      { value: 'maximum', label: 'Maximum Security', desc: 'Military-grade protection' }
                    ].map((option) => (
                      <div
                        key={option.value}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          config.level === option.value
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
                        }`}
                        onClick={() => setConfig(prev => ({ ...prev, level: option.value as any }))}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`w-5 h-5 rounded-full border-2 mt-0.5 ${
                            config.level === option.value
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-gray-300'
                          }`}>
                            {config.level === option.value && (
                              <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
                            )}
                          </div>
                          <div>
                            <div className="font-medium text-gray-800">{option.label}</div>
                            <div className="text-sm text-gray-600">{option.desc}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Production Mode */}
                <div>
                  <h4 className="flex items-center gap-2 text-lg font-semibold text-gray-800 mb-4">
                    🚀 Production Mode
                  </h4>
                  <div className="space-y-4">
                    {[
                      { id: 'productionSafe', label: 'Production-Safe Mode', desc: 'Prevents HTTP 500 errors on servers', checked: config.productionSafe },
                      { id: 'loadBalancerCompatible', label: 'Load Balancer Compatible', desc: 'Works with proxies and CDNs', checked: config.loadBalancerCompatible }
                    ].map((option) => (
                      <div
                        key={option.id}
                        className="p-4 border-2 rounded-lg cursor-pointer transition-all border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50"
                        onClick={() => setConfig(prev => ({ ...prev, [option.id]: !prev[option.id as keyof EncryptionConfig] }))}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`w-11 h-6 rounded-full border-2 transition-all ${
                            option.checked ? 'bg-blue-500 border-blue-500' : 'bg-gray-200 border-gray-300'
                          }`}>
                            <div className={`w-5 h-5 bg-white rounded-full shadow-md transition-transform ${
                              option.checked ? 'translate-x-5' : 'translate-x-0'
                            }`}></div>
                          </div>
                          <div>
                            <div className="font-medium text-gray-800">{option.label}</div>
                            <div className="text-sm text-gray-600">{option.desc}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Security Options */}
                <div>
                  <h4 className="flex items-center gap-2 text-lg font-semibold text-gray-800 mb-4">
                    🛡️ Security Options
                  </h4>
                  <div className="space-y-4">
                    {[
                      { id: 'antiDebugging', label: 'Anti-Debugging', desc: 'Blocks debugging attempts', checked: config.antiDebugging },
                      { id: 'integrityCheck', label: 'Integrity Checking', desc: 'Detects file tampering', checked: config.integrityCheck },
                      { id: 'domainLocking', label: 'Domain Locking', desc: 'Restrict to specific domains', checked: config.domainLocking }
                    ].map((option) => (
                      <div
                        key={option.id}
                        className="p-4 border-2 rounded-lg cursor-pointer transition-all border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50"
                        onClick={() => setConfig(prev => ({ ...prev, [option.id]: !prev[option.id as keyof EncryptionConfig] }))}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`w-11 h-6 rounded-full border-2 transition-all ${
                            option.checked ? 'bg-blue-500 border-blue-500' : 'bg-gray-200 border-gray-300'
                          }`}>
                            <div className={`w-5 h-5 bg-white rounded-full shadow-md transition-transform ${
                              option.checked ? 'translate-x-5' : 'translate-x-0'
                            }`}></div>
                          </div>
                          <div>
                            <div className="font-medium text-gray-800">{option.label}</div>
                            <div className="text-sm text-gray-600">{option.desc}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Domain Configuration */}
              {config.domainLocking && (
                <div className="mt-6 p-5 bg-white rounded-lg border-2 border-gray-200">
                  <h4 className="flex items-center gap-2 text-lg font-semibold text-gray-800 mb-4">
                    🌐 Allowed Domains
                  </h4>
                  <div className="flex gap-3 mb-4">
                    <Input
                      placeholder="Enter domain (e.g., yoursite.com)"
                      value={config.allowedDomains.join(', ')}
                      onChange={(e) => setConfig(prev => ({ ...prev, allowedDomains: e.target.value.split(',').map(d => d.trim()).filter(d => d) }))}
                      className="flex-1"
                    />
                    <Button
                      onClick={() => {
                        const domain = prompt('Enter domain:')
                        if (domain && !config.allowedDomains.includes(domain)) {
                          setConfig(prev => ({ ...prev, allowedDomains: [...prev.allowedDomains, domain] }))
                        }
                      }}
                      className="bg-blue-500 hover:bg-blue-600"
                    >
                      Add
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {config.allowedDomains.map((domain, index) => (
                      <div key={index} className="flex items-center gap-2 bg-gray-100 px-3 py-1 rounded-full text-sm">
                        <span>{domain}</span>
                        <button
                          onClick={() => setConfig(prev => ({ ...prev, allowedDomains: prev.allowedDomains.filter((_, i) => i !== index) }))}
                          className="w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-5 justify-center mt-8">
              <Button
                onClick={startEncryption}
                disabled={selectedFiles.length === 0 || isProcessing}
                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all"
              >
                {isProcessing ? 'Processing...' : '🚀 Start Obfuscation'}
              </Button>
              <Button
                onClick={startEncryption}
                disabled={selectedFiles.length === 0 || isProcessing}
                className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all"
              >
                🔒 Create Encrypted Files
              </Button>
              <Button
                variant="outline"
                onClick={clearFiles}
                disabled={isProcessing}
                className="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white border-0 px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all"
              >
                🗑️ Clear Queue
              </Button>
              <Button
                onClick={downloadAll}
                disabled={processedFiles.length === 0}
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all"
              >
                📥 Download All
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Processed Files */}
        {processedFiles.length > 0 && (
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-4 text-xl font-semibold text-gray-800">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center text-white text-2xl">
                  ⚡
                </div>
                Processing Queue
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {processedFiles.map((file) => (
                  <div key={file.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-white font-bold ${
                          file.status === 'completed' ? 'bg-gradient-to-br from-green-500 to-green-600' :
                          file.status === 'error' ? 'bg-gradient-to-br from-red-500 to-red-600' :
                          file.status === 'processing' ? 'bg-gradient-to-br from-blue-500 to-blue-600' :
                          'bg-gradient-to-br from-yellow-500 to-yellow-600'
                        }`}>
                          {file.status === 'completed' ? '✓' :
                           file.status === 'error' ? '✗' :
                           file.status === 'processing' ? '⟳' : '⏳'}
                        </div>
                        <div>
                          <div className="font-semibold text-gray-800">{file.original_name}</div>
                          <div className="text-sm text-gray-500">
                            {formatFileSize(file.original_size)} → {formatFileSize(file.encrypted_size)}
                            {file.size_increase_percent > 0 && (
                              <span className="text-blue-600 ml-2">
                                (+{file.size_increase_percent.toFixed(1)}%)
                              </span>
                            )}
                          </div>
                          {file.error_message && (
                            <div className="text-sm text-red-600 mt-1">{file.error_message}</div>
                          )}
                        </div>
                      </div>

                      {file.status === 'completed' && (
                        <Button
                          onClick={() => downloadFile(file.id)}
                          className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all"
                        >
                          📥 Download
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
