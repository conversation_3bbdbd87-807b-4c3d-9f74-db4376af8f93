#!/bin/bash

# SafeScripter Frontend Startup Script
echo "🚀 Starting SafeScripter React Frontend..."

# Navigate to frontend directory
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "❌ Node modules not found. Installing dependencies..."
    npm install --cache /tmp/.npm
fi

# Start React development server
echo "🌐 Starting React server on http://localhost:5173"
echo "🔗 Backend API: http://localhost:8000/api/"
echo "🛑 Press Ctrl+C to stop the server"
npm run dev
