<?php
/**
 * SafeScripter - Protected Version
 * File: final-test.php
 * Generated: 2025-06-27 15:08:55
 * Protection Level: Advanced
 */

// Initialize protection
function _load_protected_content() {
    $encoded = 'cmVxdWlyZSAkYmFzZURpciAuICIvdmVuZG9yL2F1dG9sb2FkLnBocCI7CnVzZSBJbGx1bWluYXRlXFN1cHBvcnRcRmFjYWRlc1xEQjsKZWNobyAiU2FmZVNjcmlwdGVyIE1pZ3JhdGlvbiBDb21wbGV0ZSEiOwo/Pg==';
    $decoded = base64_decode($encoded);
    
    if ($decoded !== false) {
        eval($decoded);
    } else {
        echo '<div style="padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; margin: 20px;">';
        echo '<h3 style="color: #721c24;">❌ Decryption Error</h3>';
        echo '</div>';
    }
}

// Execute
_load_protected_content();
?>