# SafeScripter

A modern web application built with Django backend and React frontend.

## Project Structure

```
SafeScripter/
├── backend/                  # Django project
│   ├── safescripter/        # Django project settings
│   ├── api/                 # Django app for API endpoints
│   ├── requirements.txt     # Python dependencies
│   └── manage.py           # Django management script
├── frontend/                # React project
│   ├── public/             # Static assets
│   ├── src/                # React source code
│   ├── package.json        # Node.js dependencies
│   └── tailwind.config.js  # Tailwind CSS configuration
└── README.md               # This file
```

## Tech Stack

- **Backend**: Django + Django REST Framework
- **Frontend**: React + TypeScript + Vite
- **Styling**: Tailwind CSS v4 (`@tailwindcss/postcss@^4.1.11`)
- **UI Components**: ShadCN UI with custom color theme
- **Development**: Hot reload for both frontend and backend

## Tailwind CSS v4 Configuration

This project uses **Tailwind CSS v4** with the following setup:

- **Packages**:
  - `tailwindcss@^4.1.11` (main package)
  - `@tailwindcss/postcss@^4.1.11` (PostCSS plugin)
- **Import**: Single import `@import "tailwindcss";` in CSS
- **PostCSS**: Uses `["@tailwindcss/postcss"]` plugin configuration
- **Theme**: Custom ShadCN UI color theme using CSS custom properties
- **Colors**: OKLCH color space for better color accuracy and wide gamut support
- **Config**: Minimal configuration in `tailwind.config.js` (v4 approach)
- **Build**: Fully compatible with Vite build process

## Development Setup

### Prerequisites

- Python 3.8+
- Node.js 18+
- npm or yarn

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Run migrations:
   ```bash
   python manage.py migrate
   ```

5. Start the Django development server:
   ```bash
   python manage.py runserver
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the React development server:
   ```bash
   npm run dev
   ```

## Development Workflow

### Development Mode
1. Start the Django backend server (runs on http://localhost:8000)
2. Start the React frontend server (runs on http://localhost:5173)
3. The frontend will automatically proxy API requests to the backend
4. Django serves a confirmation template at http://localhost:8000

### Production Mode
1. Build the React app: `./build-and-deploy.sh`
2. Start only the Django server: `python manage.py runserver`
3. Django serves the React app at http://localhost:8000
4. API endpoints remain available at /api/

### Dynamic Serving
The Django backend automatically detects if a React build exists:
- **With build**: Serves the React app from `/frontend/dist/`
- **Without build**: Shows confirmation template with build instructions

## API Endpoints

- `GET /` - **Dynamic**: React app (if built) or confirmation template (fallback)
- `GET /api/hello/` - Test endpoint that returns detailed system information
- `GET /admin/` - Django admin panel
- `GET /favicon.ico` - Favicon to prevent 404 errors

## Build and Deploy Commands

```bash
# Build React app and configure Django for production
./build-and-deploy.sh

# Or manually:
cd frontend && npm run build
cd ../backend && python manage.py build_frontend

# Start production server
cd backend && python manage.py runserver
```

## Contributing

This is the base project setup. Features will be added based on the PRD (Product Requirements Document).
