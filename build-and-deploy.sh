#!/bin/bash

# SafeScripter Build and Deploy Script
echo "🚀 SafeScripter - Build and Deploy to Production Mode"
echo "===================================================="

# Check if we're in the right directory
if [ ! -f "backend/manage.py" ]; then
    echo "❌ Please run this script from the SafeScripter root directory"
    exit 1
fi

echo "📦 Step 1: Building React Frontend..."
cd frontend

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📥 Installing npm dependencies..."
    npm install --cache /tmp/.npm
fi

# Build the React app
echo "⚡ Building React app for production..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Frontend build failed!"
    exit 1
fi

echo "✅ Frontend build completed!"

# Go back to root
cd ..

echo ""
echo "🔧 Step 2: Configuring Django for production serving..."

# Use Django management command
cd backend
source venv/bin/activate
python manage.py build_frontend

if [ $? -ne 0 ]; then
    echo "❌ Django configuration failed!"
    exit 1
fi

echo ""
echo "🎉 Build and Deploy Complete!"
echo "================================"
echo ""
echo "✅ React app built successfully"
echo "✅ Django configured to serve React app"
echo ""
echo "🌐 Your app is now ready for production!"
echo "   Visit: http://localhost:8000/"
echo ""
echo "📋 To start the server:"
echo "   cd backend"
echo "   source venv/bin/activate"
echo "   python manage.py runserver"
echo ""
echo "🔄 To rebuild:"
echo "   ./build-and-deploy.sh"
echo ""
echo "💡 The Django backend will now serve:"
echo "   • React app at /"
echo "   • API endpoints at /api/"
echo "   • Admin panel at /admin/"
