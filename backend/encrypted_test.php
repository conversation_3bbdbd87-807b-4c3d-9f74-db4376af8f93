<?php
/**
 * SafeScripter - Protected Version
 * File: test.php
 * Generated: 2025-06-27 14:12:26
 * Protection Level: Basic
 */

// Initialize protection
function _load_protected_content() {
    $encoded = 'ZWNobyAiSGVsbG8gU2FmZVNjcmlwdGVyISI7ID8+';
    $decoded = base64_decode($encoded);
    
    if ($decoded !== false) {
        eval($decoded);
    } else {
        echo '<div style="padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; margin: 20px;">';
        echo '<h3 style="color: #721c24;">❌ Decryption Error</h3>';
        echo '</div>';
    }
}

// Execute
_load_protected_content();
?>