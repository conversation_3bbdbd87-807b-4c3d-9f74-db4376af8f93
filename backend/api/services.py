"""
SafeScripter Encryption Services
Provides code obfuscation and encryption functionality for the Django backend.
"""

import base64
import time
from datetime import datetime
from typing import Dict, Any, Optional
from .models import EncryptionConfiguration, ProcessedFile


class CleanEncryptionEngine:
    """
    Clean encryption engine with dependency fixes and advanced obfuscation.
    Ported from the original Flask application with Django integration.
    """
    
    def __init__(self):
        self.supported_extensions = ['.php', '.html', '.css', '.js', '.jsx', '.ts', '.tsx']
    
    def encrypt_file_content(self, content: str, filename: str, config: Optional[EncryptionConfiguration] = None) -> str:
        """
        Encrypt file content with dependency fixes and obfuscation.
        
        Args:
            content: The original file content
            filename: The original filename
            config: Encryption configuration (optional)
            
        Returns:
            Encrypted/obfuscated content
            
        Raises:
            Exception: If encryption fails
        """
        try:
            # Use default config if none provided
            if config is None:
                config = self._get_default_config()
            
            # Apply preprocessing based on file type
            processed_content = self._preprocess_content(content, filename, config)
            
            # Apply obfuscation techniques
            if config.string_encoding:
                processed_content = self._apply_string_encoding(processed_content, config)
            
            if config.control_flow:
                processed_content = self._apply_control_flow_obfuscation(processed_content, config)
            
            if config.dead_code:
                processed_content = self._inject_dead_code(processed_content, config)
            
            # Apply encryption
            encrypted_content = self._apply_encryption(processed_content, filename, config)
            
            return encrypted_content
            
        except Exception as e:
            raise Exception(f"Encryption failed for {filename}: {str(e)}")
    
    def _get_default_config(self) -> EncryptionConfiguration:
        """Get or create default encryption configuration"""
        config, created = EncryptionConfiguration.objects.get_or_create(
            name="Default Configuration",
            defaults={
                'encryption_level': 'basic',
                'production_safe': True,
                'anti_debugging': True,
                'base64': True,
                'obfuscation_strength': 70,
            }
        )
        return config
    
    def _preprocess_content(self, content: str, filename: str, config: EncryptionConfiguration) -> str:
        """Preprocess content based on file type and configuration"""
        file_ext = self._get_file_extension(filename).lower()
        
        if file_ext == '.php':
            return self._preprocess_php_content(content, config)
        elif file_ext in ['.js', '.jsx', '.ts', '.tsx']:
            return self._preprocess_javascript_content(content, config)
        elif file_ext in ['.html', '.htm']:
            return self._preprocess_html_content(content, config)
        elif file_ext == '.css':
            return self._preprocess_css_content(content, config)
        
        return content
    
    def _preprocess_php_content(self, content: str, config: EncryptionConfiguration) -> str:
        """Preprocess PHP content with Laravel dependency fixes"""
        # Fix dependency paths for Laravel files
        fixed_content = self._fix_laravel_dependencies(content)
        
        # Remove comments if configured
        if config.remove_comments:
            fixed_content = self._remove_php_comments(fixed_content)
        
        # Minify if configured
        if config.minify and config.remove_whitespace:
            fixed_content = self._minify_php(fixed_content)
        
        return fixed_content
    
    def _fix_laravel_dependencies(self, content: str) -> str:
        """Fix Laravel dependency paths (from original Flask app)"""
        # Replace problematic vendor path
        content = content.replace(
            "require $baseDir . '/vendor/autoload.php';",
            """// Try multiple vendor paths
$vendor_paths = [
    __DIR__ . '/vendor/autoload.php',
    dirname(__DIR__) . '/vendor/autoload.php',
    dirname(dirname(__DIR__)) . '/vendor/autoload.php'
];

$vendor_loaded = false;
foreach ($vendor_paths as $path) {
    if (file_exists($path)) {
        require $path;
        $vendor_loaded = true;
        break;
    }
}

if (!$vendor_loaded) {
    echo '<div style="padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; margin: 20px; font-family: Arial, sans-serif;">';
    echo '<h3 style="color: #856404;">⚠️ Laravel Dependencies Required</h3>';
    echo '<p style="color: #856404;">Please install Composer dependencies:</p>';
    echo '<pre style="background: #f8f9fa; padding: 10px; border-radius: 4px;">composer install</pre>';
    echo '</div>';
    exit;
}"""
        )
        
        # Fix dotenv loading
        content = content.replace(
            "$dotenv = Dotenv\\Dotenv::createImmutable($baseDir);",
            """$dotenv = null;
if (class_exists('Dotenv\\Dotenv')) {
    $dotenv = Dotenv\\Dotenv::createImmutable($baseDir);
}"""
        )
        
        content = content.replace(
            "$dotenv->load();",
            """if ($dotenv) {
    try {
        $dotenv->load();
    } catch (Exception $e) {
        // Continue without .env
    }
}"""
        )
        
        return content
    
    def _preprocess_javascript_content(self, content: str, config: EncryptionConfiguration) -> str:
        """Preprocess JavaScript/TypeScript content"""
        if config.remove_comments:
            content = self._remove_js_comments(content)
        
        if config.minify and config.remove_whitespace:
            content = self._minify_js(content)
        
        return content
    
    def _preprocess_html_content(self, content: str, config: EncryptionConfiguration) -> str:
        """Preprocess HTML content"""
        if config.remove_comments:
            content = self._remove_html_comments(content)
        
        if config.minify and config.remove_whitespace:
            content = self._minify_html(content)
        
        return content
    
    def _preprocess_css_content(self, content: str, config: EncryptionConfiguration) -> str:
        """Preprocess CSS content"""
        if config.remove_comments:
            content = self._remove_css_comments(content)
        
        if config.minify and config.remove_whitespace:
            content = self._minify_css(content)
        
        return content
    
    def _apply_string_encoding(self, content: str, config: EncryptionConfiguration) -> str:
        """Apply string encoding obfuscation"""
        # Simple string encoding implementation
        # In a real implementation, this would be more sophisticated
        layers = config.string_encoding_layers
        
        for _ in range(layers):
            if config.base64:
                # Apply base64 encoding to string literals
                content = self._encode_strings_base64(content)
        
        return content
    
    def _apply_control_flow_obfuscation(self, content: str, config: EncryptionConfiguration) -> str:
        """Apply control flow obfuscation"""
        # Placeholder for control flow obfuscation
        # In a real implementation, this would restructure code flow
        return content
    
    def _inject_dead_code(self, content: str, config: EncryptionConfiguration) -> str:
        """Inject dead code for reverse engineering protection"""
        # Placeholder for dead code injection
        # In a real implementation, this would inject non-functional code
        return content
    
    def _apply_encryption(self, content: str, filename: str, config: EncryptionConfiguration) -> str:
        """Apply final encryption wrapper"""
        file_ext = self._get_file_extension(filename).lower()
        
        if file_ext == '.php':
            return self._create_php_wrapper(content, filename, config)
        else:
            # For non-PHP files, use base64 encoding
            if config.base64:
                encoded = base64.b64encode(content.encode('utf-8')).decode('utf-8')
                return self._create_generic_wrapper(encoded, filename, config)
        
        return content
    
    def _create_php_wrapper(self, content: str, filename: str, config: EncryptionConfiguration) -> str:
        """Create PHP wrapper with encryption (from original Flask app)"""
        # Extract PHP content
        php_content = content
        if php_content.startswith('<?php'):
            php_content = php_content[5:].lstrip()
        
        # Encode content
        encoded = base64.b64encode(php_content.encode('utf-8')).decode('utf-8')
        
        # Create wrapper
        return f"""<?php
/**
 * SafeScripter - Protected Version
 * File: {filename}
 * Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
 * Protection Level: {config.encryption_level.title()}
 */

// Initialize protection
function _load_protected_content() {{
    $encoded = '{encoded}';
    $decoded = base64_decode($encoded);
    
    if ($decoded !== false) {{
        eval($decoded);
    }} else {{
        echo '<div style="padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; margin: 20px;">';
        echo '<h3 style="color: #721c24;">❌ Decryption Error</h3>';
        echo '</div>';
    }}
}}

// Execute
_load_protected_content();
?>"""
    
    def _create_generic_wrapper(self, encoded_content: str, filename: str, config: EncryptionConfiguration) -> str:
        """Create generic wrapper for non-PHP files"""
        file_ext = self._get_file_extension(filename).lower()
        
        if file_ext in ['.js', '.jsx', '.ts', '.tsx']:
            return f"""/*
 * SafeScripter - Protected Version
 * File: {filename}
 * Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
 */
(function() {{
    const encoded = '{encoded_content}';
    const decoded = atob(encoded);
    eval(decoded);
}})();"""
        
        return encoded_content
    
    def _get_file_extension(self, filename: str) -> str:
        """Get file extension from filename"""
        return '.' + filename.split('.')[-1] if '.' in filename else ''
    
    # Utility methods for content processing
    def _remove_php_comments(self, content: str) -> str:
        """Remove PHP comments (basic implementation)"""
        # This is a simplified implementation
        # A real implementation would use proper PHP parsing
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Remove single-line comments
            if '//' in line:
                line = line.split('//')[0]
            if '#' in line and not line.strip().startswith('#'):
                line = line.split('#')[0]
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _remove_js_comments(self, content: str) -> str:
        """Remove JavaScript comments (basic implementation)"""
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            if '//' in line:
                line = line.split('//')[0]
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _remove_html_comments(self, content: str) -> str:
        """Remove HTML comments"""
        import re
        return re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    def _remove_css_comments(self, content: str) -> str:
        """Remove CSS comments"""
        import re
        return re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
    
    def _minify_php(self, content: str) -> str:
        """Basic PHP minification"""
        lines = content.split('\n')
        minified_lines = []
        
        for line in lines:
            stripped = line.strip()
            if stripped:
                minified_lines.append(stripped)
        
        return '\n'.join(minified_lines)
    
    def _minify_js(self, content: str) -> str:
        """Basic JavaScript minification"""
        return self._minify_php(content)  # Same logic for now
    
    def _minify_html(self, content: str) -> str:
        """Basic HTML minification"""
        import re
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r'>\s+<', '><', content)
        return content.strip()
    
    def _minify_css(self, content: str) -> str:
        """Basic CSS minification"""
        import re
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r';\s*}', '}', content)
        content = re.sub(r'{\s*', '{', content)
        return content.strip()
    
    def _encode_strings_base64(self, content: str) -> str:
        """Encode string literals with base64 (basic implementation)"""
        # This is a placeholder - real implementation would parse and encode strings
        return content


class FileProcessingService:
    """Service for handling file processing operations"""
    
    def __init__(self):
        self.encryption_engine = CleanEncryptionEngine()
    
    def process_file(self, file_data: Dict[str, Any], config: Optional[EncryptionConfiguration] = None) -> ProcessedFile:
        """
        Process a single file with encryption/obfuscation.
        
        Args:
            file_data: Dictionary containing 'filename' and 'content'
            config: Encryption configuration
            
        Returns:
            ProcessedFile instance
        """
        start_time = time.time()
        
        # Create ProcessedFile instance
        processed_file = ProcessedFile(
            original_filename=file_data['filename'],
            original_content=file_data['content'],
            original_size=len(file_data['content']),
            status='processing',
            encryption_config=config,
            config_snapshot=config.to_dict() if config else {}
        )
        processed_file.save()
        
        try:
            # Generate encrypted filename
            name_parts = file_data['filename'].rsplit('.', 1)
            if len(name_parts) == 2:
                encrypted_filename = f"encrypted_{name_parts[0]}.{name_parts[1]}"
            else:
                encrypted_filename = f"encrypted_{file_data['filename']}"
            
            # Encrypt content
            encrypted_content = self.encryption_engine.encrypt_file_content(
                file_data['content'], 
                file_data['filename'], 
                config
            )
            
            # Update processed file
            processing_time = time.time() - start_time
            processed_file.encrypted_filename = encrypted_filename
            processed_file.encrypted_content = encrypted_content
            processed_file.encrypted_size = len(encrypted_content)
            processed_file.status = 'completed'
            processed_file.processing_time = processing_time
            processed_file.processed_at = datetime.now()
            processed_file.save()
            
            return processed_file
            
        except Exception as e:
            # Update with error
            processed_file.status = 'error'
            processed_file.error_message = str(e)
            processed_file.processing_time = time.time() - start_time
            processed_file.save()
            
            raise e
    
    def process_multiple_files(self, files_data: list, config: Optional[EncryptionConfiguration] = None) -> list:
        """
        Process multiple files.
        
        Args:
            files_data: List of file dictionaries
            config: Encryption configuration
            
        Returns:
            List of ProcessedFile instances
        """
        results = []
        
        for file_data in files_data:
            try:
                processed_file = self.process_file(file_data, config)
                results.append(processed_file)
            except Exception as e:
                # Continue processing other files even if one fails
                print(f"Error processing {file_data.get('filename', 'unknown')}: {e}")
                continue
        
        return results
