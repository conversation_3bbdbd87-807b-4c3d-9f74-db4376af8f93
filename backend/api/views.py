from django.shortcuts import render
from django.http import HttpResponse, HttpResponseNotFound, FileResponse
from django.conf import settings
from django.templatetags.static import static
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.parsers import MultiPartParser, JSONParser
import django
import sys
import os
import tempfile
import zipfile
import io
from datetime import datetime

from .models import EncryptionConfiguration, ProcessedFile
from .services import FileProcessingService


def index_view(request):
    """
    Dynamic index page that serves React app in production or confirmation template in development.

    Production: Serves the built React app (index.html from frontend/dist)
    Development: Shows Django backend confirmation template as fallback
    """
    # Check if React build exists
    react_build_path = settings.BASE_DIR.parent / 'frontend' / 'dist' / 'index.html'

    if react_build_path.exists():
        # Production mode: Serve React app
        try:
            with open(react_build_path, 'r', encoding='utf-8') as f:
                html_content = f.read()

            # Update asset paths to work with Django static files
            html_content = html_content.replace('/assets/', '/static/assets/')
            html_content = html_content.replace('href="/vite.svg"', f'href="{static("vite.svg")}"')

            return HttpResponse(html_content, content_type='text/html')
        except Exception as e:
            # If there's an error reading the React build, fall back to confirmation template
            pass

    # Development mode: Show confirmation template
    context = {
        'django_version': django.get_version(),
        'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        'react_build_available': react_build_path.exists(),
        'build_path': str(react_build_path),
    }
    return render(request, 'api/index.html', context)


def favicon_view(request):
    """
    Simple favicon response to prevent 404 errors.
    """
    return HttpResponse(
        content_type="image/x-icon",
        content=b'\x00\x00\x01\x00\x01\x00\x10\x10\x00\x00\x01\x00\x08\x00h\x05\x00\x00\x16\x00\x00\x00(\x00\x00\x00\x10\x00\x00\x00 \x00\x00\x00\x01\x00\x08\x00\x00\x00\x00\x00@\x05\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x01\x00\x00\x00\x01\x00\x00'
    )


class HelloApiView(APIView):
    """
    Simple API endpoint that returns a greeting message.
    This is used to test the Django-React integration.
    """
    def get(self, request, format=None):
        return Response({
            "message": "Hello from SafeScripter Django backend!",
            "status": "success",
            "app": "SafeScripter",
            "django_version": django.get_version(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "endpoints": {
                "api_hello": "/api/hello/",
                "api_health": "/api/health/",
                "api_encrypt": "/api/encrypt/",
                "api_download": "/api/download/<file_id>/",
                "api_download_all": "/api/download-all/",
                "api_clear": "/api/clear/",
                "admin": "/admin/",
                "frontend": "http://localhost:5173"
            }
        }, status=status.HTTP_200_OK)


class HealthCheckView(APIView):
    """Health check endpoint for the SafeScripter API"""

    def get(self, request, format=None):
        return Response({
            'status': 'healthy',
            'service': 'SafeScripter Django API',
            'version': '2.0.0',
            'timestamp': datetime.now().isoformat(),
            'django_version': django.get_version(),
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        }, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name='dispatch')
class EncryptFilesView(APIView):
    """
    API endpoint for encrypting files.
    Accepts JSON data with files array containing filename and content.
    """
    parser_classes = [JSONParser]

    def post(self, request, format=None):
        try:
            data = request.data
            if not data or 'files' not in data:
                return Response({
                    'error': 'No files provided',
                    'message': 'Request must contain a "files" array'
                }, status=status.HTTP_400_BAD_REQUEST)

            files_data = data['files']
            encryption_config_data = data.get('encryptionConfig', {})

            # Create or get encryption configuration
            config = self._create_encryption_config(encryption_config_data)

            # Initialize file processing service
            file_service = FileProcessingService()

            # Process files
            results = []
            successful_count = 0

            for file_data in files_data:
                try:
                    # Validate file data
                    filename = file_data.get('filename') or file_data.get('name') or 'unknown_file.php'
                    content = file_data.get('content') or file_data.get('data') or ''

                    if not content:
                        results.append({
                            'filename': filename,
                            'original_name': filename,
                            'status': 'error',
                            'error': 'Empty file content'
                        })
                        continue

                    # Process the file
                    processed_file = file_service.process_file({
                        'filename': filename,
                        'content': content
                    }, config)

                    # Add to results
                    result_data = processed_file.to_dict()
                    results.append(result_data)

                    if processed_file.status == 'completed':
                        successful_count += 1

                except Exception as e:
                    results.append({
                        'filename': file_data.get('filename', 'unknown'),
                        'original_name': file_data.get('filename', 'unknown'),
                        'status': 'error',
                        'error': str(e)
                    })

            return Response({
                'status': 'success',
                'results': results,
                'total_files': len(results),
                'successful': successful_count,
                'message': f'Processed {successful_count} of {len(results)} files successfully'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e),
                'message': 'An error occurred while processing files'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _create_encryption_config(self, config_data: dict) -> EncryptionConfiguration:
        """Create encryption configuration from request data"""
        # Extract configuration values with defaults
        config = EncryptionConfiguration(
            name=f"Config_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            encryption_level=config_data.get('level', 'basic'),
            production_safe=config_data.get('productionSafe', True),
            load_balancer_compatible=config_data.get('loadBalancerCompatible', True),
            anti_debugging=config_data.get('antiDebugging', True),
            integrity_check=config_data.get('integrityCheck', False),
            domain_locking=config_data.get('domainLocking', False),
            allowed_domains=config_data.get('allowedDomains', []),

            # Basic obfuscation (from checkboxes in frontend)
            minify=config_data.get('minify', True),
            remove_comments=config_data.get('removeComments', True),
            remove_whitespace=config_data.get('removeWhitespace', True),
            rename_variables=config_data.get('renameVariables', True),

            # Advanced obfuscation
            string_encoding=config_data.get('stringEncoding', True),
            control_flow=config_data.get('controlFlow', True),
            dead_code=config_data.get('deadCode', True),
            function_split=config_data.get('functionSplit', True),

            # Security features
            anti_debug=config_data.get('antiDebug', True),
            expiration=config_data.get('expiration', True),

            # Encryption options
            base64=config_data.get('base64', True),
            aes=config_data.get('aes', False),
            hex_encode=config_data.get('hexEncode', True),
            custom_key=config_data.get('customKey', True),

            # Advanced settings
            obfuscation_strength=config_data.get('obfuscationStrength', 70),
            dead_code_injection=config_data.get('deadCodeInjection', 50),
            string_encoding_layers=config_data.get('stringEncodingLayers', 3),
        )
        config.save()
        return config


class DownloadFileView(APIView):
    """Download a single encrypted file by ID"""

    def get(self, request, file_id, format=None):
        try:
            # Get the processed file
            processed_file = ProcessedFile.objects.get(id=file_id)

            if processed_file.status != 'completed':
                return Response({
                    'error': 'File not ready for download',
                    'status': processed_file.status
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.php')
            temp_file.write(processed_file.encrypted_content)
            temp_file.close()

            # Return file response
            response = FileResponse(
                open(temp_file.name, 'rb'),
                as_attachment=True,
                filename=processed_file.encrypted_filename
            )
            response['Content-Type'] = 'application/x-php'
            return response

        except ProcessedFile.DoesNotExist:
            return Response({
                'error': 'File not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DownloadAllFilesView(APIView):
    """Download all completed files as a ZIP archive"""

    def get(self, request, format=None):
        try:
            # Get all completed files
            completed_files = ProcessedFile.objects.filter(status='completed')

            if not completed_files.exists():
                return Response({
                    'error': 'No files available for download'
                }, status=status.HTTP_404_NOT_FOUND)

            # Create ZIP file in memory
            zip_buffer = io.BytesIO()

            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for processed_file in completed_files:
                    zip_file.writestr(
                        processed_file.encrypted_filename,
                        processed_file.encrypted_content
                    )

            zip_buffer.seek(0)

            # Create response
            response = HttpResponse(
                zip_buffer.getvalue(),
                content_type='application/zip'
            )
            response['Content-Disposition'] = f'attachment; filename="encrypted_files_{datetime.now().strftime("%Y%m%d_%H%M%S")}.zip"'

            return response

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
class ClearFilesView(APIView):
    """Clear all processed files"""

    def post(self, request, format=None):
        try:
            # Delete all processed files
            deleted_count = ProcessedFile.objects.all().delete()[0]

            return Response({
                'status': 'success',
                'message': f'Cleared {deleted_count} files',
                'deleted_count': deleted_count
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetProcessedFilesView(APIView):
    """Get list of all processed files"""

    def get(self, request, format=None):
        try:
            files = ProcessedFile.objects.all().order_by('-created_at')

            results = []
            for file in files:
                results.append(file.to_dict())

            return Response({
                'status': 'success',
                'files': results,
                'total_count': len(results),
                'completed_count': len([f for f in results if f['status'] == 'completed']),
                'pending_count': len([f for f in results if f['status'] == 'pending']),
                'processing_count': len([f for f in results if f['status'] == 'processing']),
                'error_count': len([f for f in results if f['status'] == 'error']),
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
