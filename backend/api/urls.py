from django.urls import path
from .views import (
    HelloApiView,
    HealthCheckView,
    EncryptFilesView,
    DownloadFileView,
    DownloadAllFilesView,
    ClearFilesView,
    GetProcessedFilesView
)

urlpatterns = [
    path('hello/', HelloApiView.as_view(), name='hello-api'),
    path('health/', HealthCheckView.as_view(), name='health-check'),
    path('encrypt/', EncryptFilesView.as_view(), name='encrypt-files'),
    path('download/<uuid:file_id>/', DownloadFileView.as_view(), name='download-file'),
    path('download-all/', DownloadAllFilesView.as_view(), name='download-all'),
    path('clear/', ClearFilesView.as_view(), name='clear-files'),
    path('files/', GetProcessedFilesView.as_view(), name='get-files'),
]
