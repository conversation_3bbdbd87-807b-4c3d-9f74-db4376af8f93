import os
import subprocess
import shutil
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings


class Command(BaseCommand):
    help = 'Build React frontend and prepare for Django serving'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clean',
            action='store_true',
            help='Clean existing build before building',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Building SafeScripter React Frontend...'))
        
        # Define paths
        project_root = settings.BASE_DIR.parent
        frontend_dir = project_root / 'frontend'
        dist_dir = frontend_dir / 'dist'
        
        # Check if frontend directory exists
        if not frontend_dir.exists():
            raise CommandError(f'Frontend directory not found: {frontend_dir}')
        
        # Clean existing build if requested
        if options['clean'] and dist_dir.exists():
            self.stdout.write('🧹 Cleaning existing build...')
            shutil.rmtree(dist_dir)
        
        # Change to frontend directory
        original_cwd = os.getcwd()
        os.chdir(frontend_dir)
        
        try:
            # Check if node_modules exists
            if not (frontend_dir / 'node_modules').exists():
                self.stdout.write('📦 Installing npm dependencies...')
                result = subprocess.run(['npm', 'install', '--cache', '/tmp/.npm'], 
                                      capture_output=True, text=True)
                if result.returncode != 0:
                    raise CommandError(f'npm install failed: {result.stderr}')
            
            # Build the React app
            self.stdout.write('⚡ Building React app...')
            result = subprocess.run(['npm', 'run', 'build'], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                raise CommandError(f'Build failed: {result.stderr}')
            
            # Verify build output
            if not dist_dir.exists():
                raise CommandError('Build completed but dist directory not found')
            
            index_html = dist_dir / 'index.html'
            if not index_html.exists():
                raise CommandError('Build completed but index.html not found')
            
            # Success message
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ Frontend build completed successfully!\n'
                    f'📁 Build output: {dist_dir}\n'
                    f'🌐 Django will now serve the React app at http://localhost:8000/'
                )
            )
            
        except subprocess.CalledProcessError as e:
            raise CommandError(f'Build process failed: {e}')
        except Exception as e:
            raise CommandError(f'Unexpected error: {e}')
        finally:
            # Return to original directory
            os.chdir(original_cwd)
        
        # Additional instructions
        self.stdout.write(
            self.style.WARNING(
                '\n📋 Next steps:\n'
                '1. Restart Django server: python manage.py runserver\n'
                '2. Visit http://localhost:8000/ to see your React app\n'
                '3. API endpoints remain available at /api/\n'
            )
        )
