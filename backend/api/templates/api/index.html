<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeScripter API - Django Backend</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(34, 197, 94, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            border: 1px solid rgba(34, 197, 94, 0.3);
            margin-bottom: 2rem;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            background: #22c55e;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .info-card h3 {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
        }
        
        .info-card p {
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .api-links {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 2rem;
        }
        
        .api-link {
            color: white;
            text-decoration: none;
            padding: 0.75rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .api-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .frontend-link {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .frontend-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .tech-stack {
            margin-top: 2rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">SafeScripter</div>
        <div class="subtitle">Django Backend API Server</div>
        
        <div class="status">
            <div class="status-dot"></div>
            <span>Server Running</span>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3>Django Version</h3>
                <p>{{ django_version }}</p>
            </div>
            <div class="info-card">
                <h3>Python Version</h3>
                <p>{{ python_version }}</p>
            </div>
            <div class="info-card">
                <h3>Environment</h3>
                <p>{% if react_build_available %}Production{% else %}Development{% endif %}</p>
            </div>
            <div class="info-card">
                <h3>React Build</h3>
                <p>{% if react_build_available %}✅ Available{% else %}❌ Not Built{% endif %}</p>
            </div>
        </div>
        
        {% if not react_build_available %}
        <div style="background: rgba(255, 193, 7, 0.2); padding: 1rem; border-radius: 10px; border: 1px solid rgba(255, 193, 7, 0.3); margin-bottom: 2rem;">
            <h3 style="margin-bottom: 0.5rem;">🚀 Build React App for Production</h3>
            <p style="margin-bottom: 1rem; opacity: 0.9;">To serve the React app from Django, build it first:</p>
            <code style="background: rgba(0, 0, 0, 0.3); padding: 0.5rem; border-radius: 5px; display: block; font-family: monospace;">
                cd frontend && npm run build
            </code>
            <p style="margin-top: 0.5rem; font-size: 0.9rem; opacity: 0.8;">
                Build path: {{ build_path }}
            </p>
        </div>
        {% endif %}

        <div class="api-links">
            <a href="/api/hello/" class="api-link">
                🔗 Test API Endpoint - /api/hello/
            </a>
            <a href="/admin/" class="api-link">
                ⚙️ Django Admin Panel - /admin/
            </a>
        </div>
        
        <a href="http://localhost:5173" class="frontend-link">
            🚀 Open React Frontend
        </a>
        
        <div class="tech-stack">
            <strong>Tech Stack:</strong> Django {{ django_version }} + REST Framework + CORS + SQLite
        </div>
    </div>
</body>
</html>
