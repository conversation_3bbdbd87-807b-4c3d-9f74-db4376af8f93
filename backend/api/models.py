from django.db import models
import uuid


class EncryptionConfiguration(models.Model):
    """Model to store encryption configuration settings"""

    # Encryption levels
    ENCRYPTION_LEVELS = [
        ('basic', 'Basic Protection'),
        ('advanced', 'Advanced Encryption'),
        ('maximum', 'Maximum Security'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, default="Default Configuration")

    # Encryption settings
    encryption_level = models.CharField(max_length=20, choices=ENCRYPTION_LEVELS, default='basic')
    production_safe = models.BooleanField(default=True)
    load_balancer_compatible = models.BooleanField(default=True)
    anti_debugging = models.BooleanField(default=True)
    integrity_check = models.BooleanField(default=False)
    domain_locking = models.BooleanField(default=False)
    allowed_domains = models.JSONField(default=list, blank=True)

    # Basic obfuscation options
    minify = models.BooleanField(default=True)
    remove_comments = models.BooleanField(default=True)
    remove_whitespace = models.BooleanField(default=True)
    rename_variables = models.BooleanField(default=True)

    # Advanced obfuscation options
    string_encoding = models.BooleanField(default=True)
    control_flow = models.BooleanField(default=True)
    dead_code = models.BooleanField(default=True)
    function_split = models.BooleanField(default=True)

    # Security features
    anti_debug = models.BooleanField(default=True)
    expiration = models.BooleanField(default=True)

    # Encryption options
    base64 = models.BooleanField(default=True)
    aes = models.BooleanField(default=False)
    hex_encode = models.BooleanField(default=True)
    custom_key = models.BooleanField(default=True)

    # Advanced configuration
    obfuscation_strength = models.IntegerField(default=70, help_text="Percentage (10-100)")
    dead_code_injection = models.IntegerField(default=50, help_text="Percentage (0-100)")
    string_encoding_layers = models.IntegerField(default=3, help_text="Number of layers (1-10)")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.encryption_level})"

    def to_dict(self):
        """Convert configuration to dictionary for API responses"""
        return {
            'id': str(self.id),
            'name': self.name,
            'encryption_level': self.encryption_level,
            'production_safe': self.production_safe,
            'load_balancer_compatible': self.load_balancer_compatible,
            'anti_debugging': self.anti_debugging,
            'integrity_check': self.integrity_check,
            'domain_locking': self.domain_locking,
            'allowed_domains': self.allowed_domains,
            'minify': self.minify,
            'remove_comments': self.remove_comments,
            'remove_whitespace': self.remove_whitespace,
            'rename_variables': self.rename_variables,
            'string_encoding': self.string_encoding,
            'control_flow': self.control_flow,
            'dead_code': self.dead_code,
            'function_split': self.function_split,
            'anti_debug': self.anti_debug,
            'expiration': self.expiration,
            'base64': self.base64,
            'aes': self.aes,
            'hex_encode': self.hex_encode,
            'custom_key': self.custom_key,
            'obfuscation_strength': self.obfuscation_strength,
            'dead_code_injection': self.dead_code_injection,
            'string_encoding_layers': self.string_encoding_layers,
        }


class ProcessedFile(models.Model):
    """Model to store processed/encrypted files"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('error', 'Error'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # File information
    original_filename = models.CharField(max_length=255)
    encrypted_filename = models.CharField(max_length=255)
    original_content = models.TextField()
    encrypted_content = models.TextField(blank=True)

    # File metadata
    original_size = models.IntegerField(default=0)
    encrypted_size = models.IntegerField(default=0)
    file_type = models.CharField(max_length=50, default='php')

    # Processing information
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    error_message = models.TextField(blank=True)
    processing_time = models.FloatField(null=True, blank=True, help_text="Processing time in seconds")

    # Configuration used
    encryption_config = models.ForeignKey(
        EncryptionConfiguration,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    config_snapshot = models.JSONField(default=dict, help_text="Snapshot of config at processing time")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.original_filename} ({self.status})"

    @property
    def size_increase_percent(self):
        """Calculate size increase percentage"""
        if self.original_size == 0:
            return 0
        return ((self.encrypted_size - self.original_size) / self.original_size) * 100

    @property
    def compression_ratio(self):
        """Calculate compression ratio"""
        if self.original_size == 0:
            return 1.0
        return round(self.encrypted_size / self.original_size, 2)

    def to_dict(self):
        """Convert file to dictionary for API responses"""
        return {
            'id': str(self.id),
            'filename': self.original_filename,
            'original_name': self.original_filename,
            'encrypted_name': self.encrypted_filename,
            'size': self.encrypted_size,
            'original_size': self.original_size,
            'encrypted_size': self.encrypted_size,
            'size_increase_percent': self.size_increase_percent,
            'compression_ratio': self.compression_ratio,
            'status': self.status,
            'error_message': self.error_message,
            'processing_time': self.processing_time,
            'download_url': f'/api/download/{self.id}/',
            'downloadUrl': f'/api/download/{self.id}/',
            'protection_level': 95,  # High security level
            'encryption_config': self.config_snapshot,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
        }
