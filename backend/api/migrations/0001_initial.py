# Generated by Django 5.2.3 on 2025-06-27 14:00

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='EncryptionConfiguration',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(default='Default Configuration', max_length=100)),
                ('encryption_level', models.CharField(choices=[('basic', 'Basic Protection'), ('advanced', 'Advanced Encryption'), ('maximum', 'Maximum Security')], default='basic', max_length=20)),
                ('production_safe', models.BooleanField(default=True)),
                ('load_balancer_compatible', models.<PERSON>oleanField(default=True)),
                ('anti_debugging', models.BooleanField(default=True)),
                ('integrity_check', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('domain_locking', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('allowed_domains', models.JSONField(blank=True, default=list)),
                ('minify', models.<PERSON>oleanField(default=True)),
                ('remove_comments', models.BooleanField(default=True)),
                ('remove_whitespace', models.BooleanField(default=True)),
                ('rename_variables', models.BooleanField(default=True)),
                ('string_encoding', models.BooleanField(default=True)),
                ('control_flow', models.BooleanField(default=True)),
                ('dead_code', models.BooleanField(default=True)),
                ('function_split', models.BooleanField(default=True)),
                ('anti_debug', models.BooleanField(default=True)),
                ('expiration', models.BooleanField(default=True)),
                ('base64', models.BooleanField(default=True)),
                ('aes', models.BooleanField(default=False)),
                ('hex_encode', models.BooleanField(default=True)),
                ('custom_key', models.BooleanField(default=True)),
                ('obfuscation_strength', models.IntegerField(default=70, help_text='Percentage (10-100)')),
                ('dead_code_injection', models.IntegerField(default=50, help_text='Percentage (0-100)')),
                ('string_encoding_layers', models.IntegerField(default=3, help_text='Number of layers (1-10)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProcessedFile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('original_filename', models.CharField(max_length=255)),
                ('encrypted_filename', models.CharField(max_length=255)),
                ('original_content', models.TextField()),
                ('encrypted_content', models.TextField(blank=True)),
                ('original_size', models.IntegerField(default=0)),
                ('encrypted_size', models.IntegerField(default=0)),
                ('file_type', models.CharField(default='php', max_length=50)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('error', 'Error')], default='pending', max_length=20)),
                ('error_message', models.TextField(blank=True)),
                ('processing_time', models.FloatField(blank=True, help_text='Processing time in seconds', null=True)),
                ('config_snapshot', models.JSONField(default=dict, help_text='Snapshot of config at processing time')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('encryption_config', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='api.encryptionconfiguration')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
