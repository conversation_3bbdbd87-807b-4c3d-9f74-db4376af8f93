#!/bin/bash

# SafeScripter Backend Startup Script
echo "🚀 Starting SafeScripter Django Backend..."

# Navigate to backend directory
cd backend

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please run setup first."
    echo "Run: python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source venv/bin/activate

# Check if Django is installed
if ! python -c "import django" 2>/dev/null; then
    echo "❌ Django not found. Installing dependencies..."
    pip install -r requirements.txt
fi

# Run migrations
echo "🔄 Running database migrations..."
python manage.py migrate

# Start Django development server
echo "🌐 Starting Django server on http://localhost:8000"
echo "📡 API available at http://localhost:8000/api/"
echo "🛑 Press Ctrl+C to stop the server"
python manage.py runserver
